// Import auth service
import { authService } from "@/lib/auth";

// Default API configuration for Eldorado
const ELDORADO_API_CONFIG = {
  baseUrl: "https://api.eldorado.gg/api", // Replace with actual Eldorado API base URL
  defaultHeaders: {
    "Content-Type": "application/json",
    // Authorization header will be added dynamically
  },
};

// Helper function to get auth headers with token from auth service
const getEldoradoAuthHeaders = () => {
  const headers: Record<string, string> = { ...ELDORADO_API_CONFIG.defaultHeaders };
  const token = authService.getToken();

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  return headers;
};

// Define interfaces for Eldorado API structures
interface EldoradoOfferItem {
  id: string;
  title: string;
  description: string;
  price: number;
  currency: string;
  status: string;
  gameId: number;
  categoryId: number;
  createdAt: string;
  updatedAt: string;
  url?: string;
}

interface EldoradoOffersResponseData {
  total: number;
  items: EldoradoOfferItem[];
  page: number;
  limit: number;
}

interface EldoradoOffersResponse {
  success: boolean;
  data: EldoradoOffersResponseData;
  message?: string;
}

// Interface for Eldorado offer details
export interface EldoradoOfferDetails {
  id: string;
  title: string;
  description: string;
  price: number;
  currency: string;
  status: string;
  gameId: number;
  categoryId: number;
  serverId?: number;
  images: string[];
  deliveryInfo?: {
    method: string;
    instructions: string;
    accountDetails?: {
      username: string;
      password: string;
      email: string;
      additionalInfo?: string;
    };
  };
  createdAt: string;
  updatedAt: string;
}

export interface EldoradoOfferDetailsResponse {
  success: boolean;
  data: EldoradoOfferDetails;
  message?: string;
}

// Parameters for getting offers
export interface GetEldoradoOffersParams {
  page?: number;
  limit?: number;
  gameId?: number;
  categoryId?: number;
  status?: string;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

// Parameters for getting offer details
export interface GetEldoradoOfferDetailsParams {
  offerId: string;
}

// Parameters for creating offers
export interface CreateEldoradoOfferParams {
  payload: Partial<EldoradoOfferDetails>;
}

// Parameters for updating offers
export interface UpdateEldoradoOfferParams {
  offerId: string;
  payload: Partial<EldoradoOfferDetails>;
}

// Response for create/update operations
export interface EldoradoOfferMutationResponse {
  success: boolean;
  data: EldoradoOfferDetails;
  message?: string;
}

/**
 * Eldorado offers service
 */
export const eldoradoOffersService = {
  /**
   * Get offers with pagination, sorting and filtering options
   *
   * @param page Current page (default: 1)
   * @param limit Number of items per page (default: 10)
   * @param gameId Filter by game ID
   * @param categoryId Filter by category ID
   * @param status Filter by status
   * @param sortBy Field to sort by
   * @param sortOrder Sort direction
   * @returns Promise with the offers data
   */
  getOffers: async ({
    page = 1,
    limit = 10,
    gameId,
    categoryId,
    status,
    sortBy,
    sortOrder,
  }: GetEldoradoOffersParams = {}): Promise<EldoradoOffersResponse> => {
    try {
      const url = new URL(`${ELDORADO_API_CONFIG.baseUrl}/offers`);

      // Add query parameters
      url.searchParams.append("page", page.toString());
      url.searchParams.append("limit", limit.toString());

      if (gameId !== undefined) {
        url.searchParams.append("gameId", gameId.toString());
      }
      if (categoryId !== undefined) {
        url.searchParams.append("categoryId", categoryId.toString());
      }
      if (status) {
        url.searchParams.append("status", status);
      }
      if (sortBy) {
        url.searchParams.append("sortBy", sortBy);
      }
      if (sortOrder) {
        url.searchParams.append("sortOrder", sortOrder);
      }

      const response = await fetch(url.toString(), {
        method: "GET",
        headers: getEldoradoAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error(`Eldorado API error: ${response.status}`);
      }

      return (await response.json()) as EldoradoOffersResponse;
    } catch (error) {
      console.error("Failed to fetch Eldorado offers:", error);
      throw error;
    }
  },

  /**
   * Get details for a specific Eldorado offer
   *
   * @param offerId The ID of the offer to fetch details for
   * @returns Promise with the offer details data
   */
  getOfferDetails: async ({
    offerId,
  }: GetEldoradoOfferDetailsParams): Promise<EldoradoOfferDetailsResponse> => {
    try {
      const url = new URL(`${ELDORADO_API_CONFIG.baseUrl}/offers/${offerId}`);

      const response = await fetch(url.toString(), {
        method: "GET",
        headers: getEldoradoAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error(`Eldorado API error: ${response.status}`);
      }

      return (await response.json()) as EldoradoOfferDetailsResponse;
    } catch (error) {
      console.error(`Failed to fetch Eldorado offer details for ${offerId}:`, error);
      throw error;
    }
  },

  /**
   * Create a new Eldorado offer
   *
   * @param payload The data to create the offer with
   * @returns Promise with the created offer response
   */
  createOffer: async ({
    payload,
  }: CreateEldoradoOfferParams): Promise<EldoradoOfferMutationResponse> => {
    try {
      const url = new URL(`${ELDORADO_API_CONFIG.baseUrl}/offers`);

      const response = await fetch(url.toString(), {
        method: "POST",
        headers: getEldoradoAuthHeaders(),
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorBody = await response.text();
        console.error("Eldorado API HTTP Error Response:", errorBody);
        throw new Error(
          `Eldorado API error: ${response.status} - ${response.statusText}`
        );
      }

      const responseData = (await response.json()) as EldoradoOfferMutationResponse;

      // Check for logical errors within the 200 OK response
      if (!responseData.success) {
        console.error("Eldorado API Logical Error Response:", responseData);
        throw new Error(
          responseData.message || "Eldorado API indicated failure"
        );
      }

      return responseData;
    } catch (error) {
      console.error("Failed to create Eldorado offer:", error);
      throw error;
    }
  },

  /**
   * Update an existing Eldorado offer
   *
   * @param offerId The ID of the offer to update
   * @param payload The data to update the offer with
   * @returns Promise with the updated offer response
   */
  updateOffer: async ({
    offerId,
    payload,
  }: UpdateEldoradoOfferParams): Promise<EldoradoOfferMutationResponse> => {
    try {
      const url = new URL(`${ELDORADO_API_CONFIG.baseUrl}/offers/${offerId}`);

      const response = await fetch(url.toString(), {
        method: "PUT",
        headers: getEldoradoAuthHeaders(),
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorBody = await response.text();
        console.error("Eldorado API HTTP Error Response:", errorBody);
        throw new Error(
          `Eldorado API error: ${response.status} - ${response.statusText}`
        );
      }

      const responseData = (await response.json()) as EldoradoOfferMutationResponse;

      // Check for logical errors within the 200 OK response
      if (!responseData.success) {
        console.error("Eldorado API Logical Error Response:", responseData);
        throw new Error(
          responseData.message || "Eldorado API indicated failure"
        );
      }

      return responseData;
    } catch (error) {
      console.error(`Failed to update Eldorado offer ${offerId}:`, error);
      throw error;
    }
  },
};

/**
 * Eldorado media service
 */
export const eldoradoMediaService = {
  /**
   * Upload an image to Eldorado
   *
   * @param file The image file to upload
   * @returns Promise with the upload response data
   */
  uploadImage: async (file: File): Promise<{ success: boolean; data: { url: string; id: string }; message?: string }> => {
    try {
      const url = new URL(`${ELDORADO_API_CONFIG.baseUrl}/media/upload`);

      const formData = new FormData();
      formData.append("file", file);

      // Don't include Content-Type for FormData requests
      // but do include Authorization if available
      const headers: Record<string, string> = {};
      const token = authService.getToken();

      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }

      const response = await fetch(url.toString(), {
        method: "POST",
        headers,
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Eldorado API error: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Failed to upload image to Eldorado:", error);
      throw error;
    }
  },
};

export default {
  offers: eldoradoOffersService,
  media: eldoradoMediaService,
};